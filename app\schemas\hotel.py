"""
Hotel schemas for WhatsApp Hotel Bot application
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, validator, root_validator
import re

from app.validators.security_validators import (
    SecureBaseModel,
    SafeStr,
    SafePhone,
    validate_safe_text,
    validate_safe_phone
)


class TimestampMixin(BaseModel):
    """Mixin for timestamp fields in schemas"""
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    updated_at: datetime = Field(..., description="Timestamp when the record was last updated")


class HotelBase(SecureBaseModel):
    """Base hotel schema with common fields and security validation"""

    name: SafeStr = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Hotel name"
    )

    whatsapp_number: SafePhone = Field(
        ...,
        min_length=8,
        max_length=20,
        description="WhatsApp phone number for the hotel"
    )

    @validator('whatsapp_number')
    def validate_whatsapp_number(cls, v):
        """Enhanced WhatsApp number validation with security"""
        # SafePhone already handles basic validation, add additional checks
        if not v.startswith('+'):
            raise ValueError('WhatsApp number must include country code with +')
        return v

    @validator('name')
    def validate_name(cls, v):
        """Enhanced hotel name validation with security"""
        # SafeStr already handles sanitization, add business logic validation
        if len(v.strip()) < 2:
            raise ValueError('Hotel name must be at least 2 characters')
        return v


class HotelCreate(HotelBase):
    """Schema for creating a new hotel"""
    
    green_api_instance_id: Optional[str] = Field(
        None,
        max_length=50,
        description="Green API instance ID for WhatsApp integration"
    )
    
    green_api_token: Optional[str] = Field(
        None,
        max_length=255,
        description="Green API token for WhatsApp integration"
    )
    
    green_api_webhook_token: Optional[str] = Field(
        None,
        max_length=255,
        description="Green API webhook token for secure webhook validation"
    )
    
    settings: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Hotel-specific settings and configuration"
    )
    
    is_active: bool = Field(
        default=True,
        description="Whether the hotel is active and can receive messages"
    )
    
    @root_validator
    def validate_green_api_credentials(cls, values):
        """Validate Green API credentials consistency"""
        instance_id = values.get('green_api_instance_id')
        token = values.get('green_api_token')
        
        # If one is provided, both should be provided
        if (instance_id and not token) or (token and not instance_id):
            raise ValueError('Both Green API instance ID and token must be provided together')
        
        return values


class HotelUpdate(BaseModel):
    """Schema for updating an existing hotel"""
    
    name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=255,
        description="Hotel name"
    )
    
    whatsapp_number: Optional[str] = Field(
        None,
        min_length=8,
        max_length=20,
        description="WhatsApp phone number for the hotel"
    )
    
    green_api_instance_id: Optional[str] = Field(
        None,
        max_length=50,
        description="Green API instance ID for WhatsApp integration"
    )
    
    green_api_token: Optional[str] = Field(
        None,
        max_length=255,
        description="Green API token for WhatsApp integration"
    )
    
    green_api_webhook_token: Optional[str] = Field(
        None,
        max_length=255,
        description="Green API webhook token for secure webhook validation"
    )
    
    settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Hotel-specific settings and configuration"
    )
    
    is_active: Optional[bool] = Field(
        None,
        description="Whether the hotel is active and can receive messages"
    )
    
    @validator('whatsapp_number')
    def validate_whatsapp_number(cls, v):
        """Validate WhatsApp number format"""
        if v is not None and not re.match(r'^\+?[1-9]\d{1,14}$', v):
            raise ValueError('Invalid WhatsApp number format')
        return v
    
    @validator('name')
    def validate_name(cls, v):
        """Validate hotel name"""
        if v is not None and not v.strip():
            raise ValueError('Hotel name cannot be empty')
        return v.strip() if v else v


class HotelResponse(HotelBase, TimestampMixin):
    """Schema for hotel response"""
    
    id: uuid.UUID = Field(..., description="Hotel unique identifier")
    
    green_api_instance_id: Optional[str] = Field(
        None,
        description="Green API instance ID for WhatsApp integration"
    )
    
    # Note: We don't expose tokens in responses for security
    has_green_api_credentials: bool = Field(
        ...,
        description="Whether hotel has Green API credentials configured"
    )
    
    settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="Hotel-specific settings and configuration"
    )
    
    is_active: bool = Field(
        ...,
        description="Whether the hotel is active and can receive messages"
    )
    
    is_operational: bool = Field(
        ...,
        description="Whether hotel can send/receive messages (active + has credentials)"
    )
    
    class Config:
        from_attributes = True


class HotelListResponse(BaseModel):
    """Schema for paginated hotel list response"""
    
    hotels: List[HotelResponse] = Field(..., description="List of hotels")
    total: int = Field(..., description="Total number of hotels")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")
    
    class Config:
        from_attributes = True


class HotelSearchParams(BaseModel):
    """Schema for hotel search parameters"""
    
    name: Optional[str] = Field(
        None,
        description="Search by hotel name (partial match)"
    )
    
    whatsapp_number: Optional[str] = Field(
        None,
        description="Search by WhatsApp number (exact match)"
    )
    
    is_active: Optional[bool] = Field(
        None,
        description="Filter by active status"
    )
    
    has_credentials: Optional[bool] = Field(
        None,
        description="Filter by Green API credentials presence"
    )
    
    page: int = Field(
        default=1,
        ge=1,
        description="Page number"
    )
    
    size: int = Field(
        default=20,
        ge=1,
        le=100,
        description="Page size"
    )
    
    sort_by: str = Field(
        default="name",
        description="Sort field (name, created_at, updated_at)"
    )
    
    sort_order: str = Field(
        default="asc",
        description="Sort order (asc, desc)"
    )
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        """Validate sort field"""
        allowed_fields = ['name', 'created_at', 'updated_at', 'whatsapp_number']
        if v not in allowed_fields:
            raise ValueError(f'Sort field must be one of: {", ".join(allowed_fields)}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        """Validate sort order"""
        if v.lower() not in ['asc', 'desc']:
            raise ValueError('Sort order must be "asc" or "desc"')
        return v.lower()


class HotelConfigUpdate(BaseModel):
    """Schema for updating hotel configuration"""
    
    settings: Dict[str, Any] = Field(
        ...,
        description="Hotel settings to update"
    )
    
    merge: bool = Field(
        default=True,
        description="Whether to merge with existing settings or replace completely"
    )


class HotelStatusUpdate(BaseModel):
    """Schema for updating hotel status"""
    
    is_active: bool = Field(
        ...,
        description="Whether the hotel should be active"
    )
    
    reason: Optional[str] = Field(
        None,
        max_length=500,
        description="Reason for status change"
    )


# Export all schemas
__all__ = [
    'HotelBase',
    'HotelCreate',
    'HotelUpdate',
    'HotelResponse',
    'HotelListResponse',
    'HotelSearchParams',
    'HotelConfigUpdate',
    'HotelStatusUpdate'
]
