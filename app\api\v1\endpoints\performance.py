"""
Performance monitoring and optimization endpoints
Provides API access to performance metrics and optimization status
"""

from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import PlainTextResponse

from app.core.performance_integration import get_performance_status
from app.monitoring.performance_dashboard import get_performance_metrics_collector
from app.core.auth import get_current_admin_user
from app.core.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()


@router.get("/status", response_model=Dict[str, Any])
async def get_performance_optimization_status(
    current_user = Depends(get_current_admin_user)
):
    """Get status of all performance optimizations"""
    try:
        status = await get_performance_status()
        return {
            "status": "success",
            "data": status
        }
    except Exception as e:
        logger.error("Failed to get performance status", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get performance status")


@router.get("/metrics", response_model=Dict[str, Any])
async def get_performance_metrics(
    current_user = Depends(get_current_admin_user)
):
    """Get comprehensive performance metrics"""
    try:
        collector = get_performance_metrics_collector()
        metrics = await collector.collect_all_metrics()
        
        return {
            "status": "success",
            "data": metrics
        }
    except Exception as e:
        logger.error("Failed to get performance metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get performance metrics")


@router.get("/metrics/prometheus", response_class=PlainTextResponse)
async def get_prometheus_metrics(
    current_user = Depends(get_current_admin_user)
):
    """Get Prometheus-formatted metrics"""
    try:
        collector = get_performance_metrics_collector()
        metrics_text = collector.get_prometheus_metrics()
        
        return PlainTextResponse(
            content=metrics_text,
            media_type="text/plain; version=0.0.4; charset=utf-8"
        )
    except Exception as e:
        logger.error("Failed to get Prometheus metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get Prometheus metrics")


@router.get("/alerts", response_model=Dict[str, Any])
async def get_performance_alerts(
    current_user = Depends(get_current_admin_user)
):
    """Get active performance alerts"""
    try:
        collector = get_performance_metrics_collector()
        alerts = collector.get_active_alerts()
        
        return {
            "status": "success",
            "data": {
                "alerts": alerts,
                "count": len(alerts)
            }
        }
    except Exception as e:
        logger.error("Failed to get performance alerts", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get performance alerts")


@router.get("/database/pool", response_model=Dict[str, Any])
async def get_database_pool_metrics(
    current_user = Depends(get_current_admin_user)
):
    """Get database connection pool metrics"""
    try:
        from app.core.database_pool import get_enhanced_pool
        
        pool = get_enhanced_pool()
        metrics = await pool.get_pool_metrics()
        performance_summary = await pool.get_performance_summary()
        
        return {
            "status": "success",
            "data": {
                "current_metrics": {
                    "pool_size": metrics.pool_size,
                    "checked_out": metrics.checked_out,
                    "checked_in": metrics.checked_in,
                    "overflow": metrics.overflow,
                    "utilization_percent": metrics.utilization_percent,
                    "avg_checkout_time_ms": metrics.avg_checkout_time_ms
                },
                "performance_summary": performance_summary
            }
        }
    except Exception as e:
        logger.error("Failed to get database pool metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get database pool metrics")


@router.get("/cache", response_model=Dict[str, Any])
async def get_cache_metrics(
    current_user = Depends(get_current_admin_user)
):
    """Get cache performance metrics"""
    try:
        from app.services.cache_service import get_cache_service
        
        cache_service = await get_cache_service()
        cache_stats = await cache_service.get_cache_stats()
        
        return {
            "status": "success",
            "data": cache_stats
        }
    except Exception as e:
        logger.error("Failed to get cache metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get cache metrics")


@router.get("/memory", response_model=Dict[str, Any])
async def get_memory_metrics(
    current_user = Depends(get_current_admin_user)
):
    """Get memory usage metrics"""
    try:
        from app.utils.memory_optimizer import get_memory_profiler
        
        profiler = get_memory_profiler()
        memory_report = profiler.get_memory_report()
        
        return {
            "status": "success",
            "data": memory_report
        }
    except Exception as e:
        logger.error("Failed to get memory metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get memory metrics")


@router.get("/queries", response_model=Dict[str, Any])
async def get_query_performance_metrics(
    current_user = Depends(get_current_admin_user)
):
    """Get query performance metrics"""
    try:
        from app.utils.query_optimizer import get_query_analyzer
        
        analyzer = get_query_analyzer()
        performance_summary = analyzer.get_performance_summary()
        
        # Get N+1 query detections
        n1_detections = analyzer.detect_n1_queries(time_window_minutes=60)
        
        return {
            "status": "success",
            "data": {
                "performance_summary": performance_summary,
                "n1_detections": [
                    {
                        "parent_query": detection.parent_query,
                        "execution_count": detection.execution_count,
                        "total_time_ms": detection.total_time_ms,
                        "suggested_fix": detection.suggested_fix
                    }
                    for detection in n1_detections
                ]
            }
        }
    except Exception as e:
        logger.error("Failed to get query performance metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get query performance metrics")


@router.get("/async", response_model=Dict[str, Any])
async def get_async_performance_metrics(
    current_user = Depends(get_current_admin_user)
):
    """Get async processing performance metrics"""
    try:
        from app.utils.async_optimizer import get_async_optimizer
        
        optimizer = get_async_optimizer()
        async_stats = optimizer.task_pool.get_performance_stats()
        
        return {
            "status": "success",
            "data": async_stats
        }
    except Exception as e:
        logger.error("Failed to get async performance metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get async performance metrics")


@router.post("/cache/clear", response_model=Dict[str, Any])
async def clear_cache(
    current_user = Depends(get_current_admin_user)
):
    """Clear all cache entries"""
    try:
        from app.services.cache_service import get_cache_service
        
        cache_service = await get_cache_service()
        
        # Clear memory cache
        await cache_service.memory_cache.clear()
        
        # Clear Redis cache (if available)
        if cache_service.redis_client:
            await cache_service.redis_client.flushdb()
        
        logger.info("Cache cleared by admin", user_id=current_user.id)
        
        return {
            "status": "success",
            "message": "Cache cleared successfully"
        }
    except Exception as e:
        logger.error("Failed to clear cache", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to clear cache")


@router.post("/memory/gc", response_model=Dict[str, Any])
async def force_garbage_collection(
    current_user = Depends(get_current_admin_user)
):
    """Force garbage collection"""
    try:
        from app.utils.memory_optimizer import get_gc_optimizer
        
        optimizer = get_gc_optimizer()
        collected = optimizer.force_collection()
        
        logger.info("Garbage collection forced by admin", 
                   user_id=current_user.id,
                   collected=collected)
        
        return {
            "status": "success",
            "data": {
                "collected": collected,
                "message": "Garbage collection completed"
            }
        }
    except Exception as e:
        logger.error("Failed to force garbage collection", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to force garbage collection")
